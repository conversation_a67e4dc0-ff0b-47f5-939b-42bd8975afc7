package com.zsmall.system.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.thread.ThreadUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.system.biz.service.IBillDetailsRepairService;
import com.zsmall.system.entity.domain.vo.billDetailsRepair.BillDetailsRepairVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 账单明细修补表控制器
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/billDetailsRepair")
public class BillDetailsRepairController extends BaseController {

    private final IBillDetailsRepairService billDetailsRepairService;

    /**
     * 修补账单数据
     * @param billNo 账单编号（必填，单个账单编号）
     * @param orderNo 订单编号（必填，多个订单号用逗号拼接）
     * @param tenantId 租户ID（必填）
     */
    @SaIgnore
    @PostMapping("/repairBillData")
    @Log(title = "账单数据修补", businessType = BusinessType.UPDATE)
    public R<String> repairBillData(@RequestParam(value = "billNo", required = true) @NotBlank(message = "账单编号不能为空") String billNo,
                                    @RequestParam(value = "orderNo", required = true) @NotBlank(message = "订单编号不能为空") String orderNo,
                                    @RequestParam(value = "tenantId", required = true) @NotBlank(message = "租户ID不能为空") String tenantId) {

        log.info("开始修补账单数据，租户ID：{}, 账单编号：{}, 订单编号：{}", tenantId, billNo, orderNo);

        ThreadUtil.execAsync(() -> {
            try {
                billDetailsRepairService.repairBillData(billNo, orderNo);
            } catch (Exception e) {
                log.error("账单数据修补异常：租户ID={}, 账单编号={}, 订单编号={}, 错误信息={}", tenantId, billNo, orderNo, e.getMessage(), e);
            }
        });

        return R.ok("账单数据修补任务已启动，请稍后查看处理结果");
    }

    /**
     * 查询修补记录列表
     * @param tenantId 租户ID
     * @param orderNo 订单号
     * @param pageQuery 分页参数
     */
    @GetMapping("/list")
    public R<List<BillDetailsRepairVo>> list(@RequestParam(value = "tenantId", required = false) String tenantId,
                                            @RequestParam(value = "orderNo", required = false) String orderNo,
                                            PageQuery pageQuery) {
        return billDetailsRepairService.queryPageList(tenantId, orderNo, pageQuery);
    }

    /**
     * 根据订单号查询修补记录
     * @param orderNos 订单号列表（逗号分隔）
     */
    @GetMapping("/getByOrderNos")
    public R<List<BillDetailsRepairVo>> getByOrderNos(@RequestParam("orderNos") @NotBlank(message = "订单号不能为空") String orderNos) {
        return billDetailsRepairService.getByOrderNos(orderNos);
    }

    /**
     * 根据租户ID和时间范围查询修补记录
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    @GetMapping("/getByTenantAndTimeRange")
    public R<List<BillDetailsRepairVo>> getByTenantAndTimeRange(@RequestParam("tenantId") @NotBlank(message = "租户ID不能为空") String tenantId,
                                                               @RequestParam(value = "startTime", required = false) String startTime,
                                                               @RequestParam(value = "endTime", required = false) String endTime) {
        return billDetailsRepairService.getByTenantAndTimeRange(tenantId, startTime, endTime);
    }

    /**
     * 查看修补差值计算结果
     * @param tenantId 租户ID
     * @param orderNos 订单号列表（逗号分隔）
     */
    @GetMapping("/getDifferenceCalculation")
    public R<?> getDifferenceCalculation(@RequestParam("tenantId") @NotBlank(message = "租户ID不能为空") String tenantId,
                                        @RequestParam("orderNos") @NotBlank(message = "订单号不能为空") String orderNos) {
        return billDetailsRepairService.calculateBillDifferences(tenantId, orderNos);
    }

    /**
     * 重新推送差值数据到ERP
     * @param tenantId 租户ID
     * @param orderNos 订单号列表（逗号分隔）
     */
    @PostMapping("/resendDifferenceToErp")
    @Log(title = "重新推送差值数据到ERP", businessType = BusinessType.UPDATE)
    public R<String> resendDifferenceToErp(@RequestParam("tenantId") @NotBlank(message = "租户ID不能为空") String tenantId,
                                          @RequestParam("orderNos") @NotBlank(message = "订单号不能为空") String orderNos) {

        ThreadUtil.execAsync(() -> {
            try {
                billDetailsRepairService.resendDifferenceToErp(tenantId, orderNos);
            } catch (Exception e) {
                log.error("重新推送差值数据到ERP异常：租户ID={}, 订单号={}, 错误信息={}", tenantId, orderNos, e.getMessage(), e);
            }
        });

        return R.ok("差值数据重新推送任务已启动");
    }

}
