package com.zsmall.system.entity.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 账单修补差值计算DTO
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Data
public class BillRepairDifferenceDTO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 供应商租户ID
     */
    private String supperTenantId;

    /**
     * 商品ItemNo
     */
    private String productSkuCode;

    /**
     * 修补前操作费
     */
    private BigDecimal beforeOperationFee;

    /**
     * 修补后操作费
     */
    private BigDecimal afterOperationFee;

    /**
     * 操作费差值
     */
    private BigDecimal operationFeeDifference;

    /**
     * 修补前尾程派送费
     */
    private BigDecimal beforeFinalDeliveryFee;

    /**
     * 修补后尾程派送费
     */
    private BigDecimal afterFinalDeliveryFee;

    /**
     * 尾程派送费差值
     */
    private BigDecimal finalDeliveryFeeDifference;

    /**
     * 修补前产品小计
     */
    private BigDecimal beforeProductSkuPrice;

    /**
     * 修补后产品小计
     */
    private BigDecimal afterProductSkuPrice;

    /**
     * 产品小计差值
     */
    private BigDecimal productSkuPriceDifference;

    /**
     * 修补前订单金额
     */
    private BigDecimal beforeOrderTotalAmount;

    /**
     * 修补后订单金额
     */
    private BigDecimal afterOrderTotalAmount;

    /**
     * 订单金额差值
     */
    private BigDecimal orderTotalAmountDifference;

    /**
     * 修补前退款金额
     */
    private BigDecimal beforeOrderRefundTotalAmount;

    /**
     * 修补后退款金额
     */
    private BigDecimal afterOrderRefundTotalAmount;

    /**
     * 退款金额差值
     */
    private BigDecimal orderRefundTotalAmountDifference;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 计算所有差值
     */
    public void calculateDifferences() {
        this.operationFeeDifference = safeDifference(afterOperationFee, beforeOperationFee);
        this.finalDeliveryFeeDifference = safeDifference(afterFinalDeliveryFee, beforeFinalDeliveryFee);
        this.productSkuPriceDifference = safeDifference(afterProductSkuPrice, beforeProductSkuPrice);
        this.orderTotalAmountDifference = safeDifference(afterOrderTotalAmount, beforeOrderTotalAmount);
        this.orderRefundTotalAmountDifference = safeDifference(afterOrderRefundTotalAmount, beforeOrderRefundTotalAmount);
    }

    /**
     * 安全的差值计算，处理null值
     */
    private BigDecimal safeDifference(BigDecimal after, BigDecimal before) {
        BigDecimal afterValue = (after != null) ? after : BigDecimal.ZERO;
        BigDecimal beforeValue = (before != null) ? before : BigDecimal.ZERO;
        return afterValue.subtract(beforeValue);
    }

    /**
     * 判断是否有差值
     */
    public boolean hasDifference() {
        return !BigDecimal.ZERO.equals(operationFeeDifference) ||
               !BigDecimal.ZERO.equals(finalDeliveryFeeDifference) ||
               !BigDecimal.ZERO.equals(productSkuPriceDifference) ||
               !BigDecimal.ZERO.equals(orderTotalAmountDifference) ||
               !BigDecimal.ZERO.equals(orderRefundTotalAmountDifference);
    }

}
