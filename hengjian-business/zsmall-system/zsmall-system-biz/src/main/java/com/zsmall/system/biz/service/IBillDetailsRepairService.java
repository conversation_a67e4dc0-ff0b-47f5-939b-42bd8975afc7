package com.zsmall.system.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.system.entity.domain.dto.BillRepairDifferenceDTO;
import com.zsmall.system.entity.domain.vo.billDetailsRepair.BillDetailsRepairVo;

import java.util.List;

/**
 * 账单明细修补表服务接口
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
public interface IBillDetailsRepairService {

    /**
     * 修补账单数据
     * @param billNo 账单编号（单个账单编号）
     * @param orderNo 订单编号（多个订单号用逗号拼接）
     */
    void repairBillData(String billNo, String orderNo);

    /**
     * 分页查询修补记录列表
     * @param tenantId 租户ID
     * @param orderNo 订单号
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    R<List<BillDetailsRepairVo>> queryPageList(String tenantId, String orderNo, PageQuery pageQuery);

    /**
     * 根据订单号查询修补记录
     * @param orderNos 订单号列表（逗号分隔）
     * @return 修补记录列表
     */
    R<List<BillDetailsRepairVo>> getByOrderNos(String orderNos);

    /**
     * 根据租户ID和时间范围查询修补记录
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 修补记录列表
     */
    R<List<BillDetailsRepairVo>> getByTenantAndTimeRange(String tenantId, String startTime, String endTime);

    /**
     * 计算账单修补前后的差值
     * @param tenantId 租户ID
     * @param orderNos 订单号列表（逗号分隔）
     * @return 差值计算结果
     */
    R<List<BillRepairDifferenceDTO>> calculateBillDifferences(String tenantId, String orderNos);

    /**
     * 重新推送差值数据到ERP
     * @param tenantId 租户ID
     * @param orderNos 订单号列表（逗号分隔）
     */
    void resendDifferenceToErp(String tenantId, String orderNos);

    /**
     * 推送修补表中未推送的全量数据到ERP（定时任务使用）
     */
    void pushUnsentRepairDataToErp();

}
