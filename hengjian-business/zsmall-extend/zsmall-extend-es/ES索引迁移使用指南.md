# Easy-Es 索引迁移完整指南

## 概述

本指南提供了一个完整的Easy-Es索引迁移解决方案，适用于手动模式下的索引字段变更、数据迁移等场景。

## 功能特性

- ✅ **完整的迁移流程**：索引创建、数据迁移、验证、别名切换
- ✅ **分布式锁支持**：防止并发迁移冲突
- ✅ **进度监控**：实时查看迁移进度和状态
- ✅ **数据验证**：确保迁移数据的完整性和一致性
- ✅ **异步执行**：支持大数据量的异步迁移
- ✅ **错误处理**：完善的异常处理和重试机制
- ✅ **别名管理**：平滑切换索引别名
- ✅ **批量处理**：分批迁移避免内存溢出

## 架构组件

### 核心服务类
- `EsIndexMigrationService`: 核心迁移服务
- `MigrationThreadPoolConfig`: 线程池配置
- `EsIndexMigrationController`: REST API控制器

### 数据传输对象
- `MigrationProgressDto`: 迁移进度信息
- `MigrationResultDto`: 迁移结果信息
- `MigrationStatusEnum`: 迁移状态枚举

## 使用场景

### 1. 字段类型变更
当您修改了实体类中的字段类型或注解时：

```java
// 修改前
@IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_SMART)
private String productName;

// 修改后
@IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD)
private String productName;
```

### 2. 新增字段
添加新字段到实体类：

```java
// 新增字段
@IndexField(fieldType = FieldType.KEYWORD)
private String newField;

@IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
private Date updateTime;
```

### 3. 删除字段
从实体类中移除不需要的字段。

### 4. 索引设置变更
修改分片数、副本数等索引设置。

## 配置要求

### 1. 修改Easy-Es配置
将索引处理模式改为手动模式：

```yaml
easy-es:
  global-config:
    process-index-mode: MANUAL  # 手动模式
```

### 2. 添加依赖注入
确保相关Bean正确注入：

```java
@Autowired
private EsIndexMigrationService migrationService;

@Autowired
private RestHighLevelClient restHighLevelClient;
```

## 使用方法

### 方式一：通过代码调用

```java
@Service
public class ProductIndexMigrationService {
    
    @Autowired
    private EsIndexMigrationService migrationService;
    
    /**
     * 执行商品索引迁移
     */
    public void migrateProductIndex() {
        String oldIndexName = "product";
        String newIndexName = "product_v2";
        String aliasName = "product_current";
        
        // 执行迁移
        MigrationResultDto result = migrationService.performIndexMigration(
            oldIndexName, newIndexName, aliasName);
        
        if (result.getSuccess()) {
            log.info("迁移成功，迁移数据量: {}", result.getMigratedCount());
        } else {
            log.error("迁移失败: {}", result.getMessage());
        }
    }
}
```

### 方式二：通过REST API调用

#### 启动迁移
```bash
POST /es/migration/start
Content-Type: application/json

{
    "oldIndexName": "product",
    "newIndexName": "product_v2",
    "aliasName": "product_current",
    "async": true
}
```

#### 查询进度
```bash
GET /es/migration/progress/{migrationId}
```

#### 查询结果
```bash
GET /es/migration/result/{migrationId}
```

#### 清理旧索引
```bash
DELETE /es/migration/cleanup/{oldIndexName}
```

## 迁移流程详解

### 1. 准备阶段
- 获取分布式锁，防止并发迁移
- 初始化迁移进度记录
- 检查源索引和目标索引状态

### 2. 索引创建
- 根据实体类注解创建新索引
- 设置正确的mapping和settings
- 验证索引创建成功

### 3. 数据迁移
- 分批查询源索引数据（默认1000条/批）
- 对数据进行处理（可自定义）
- 批量插入到新索引
- 实时更新迁移进度

### 4. 数据验证
- 验证数据总量一致性
- 抽样验证数据字段一致性
- 确保迁移质量

### 5. 别名切换
- 将别名从旧索引切换到新索引
- 实现平滑过渡，业务无感知

### 6. 完成清理
- 记录迁移结果
- 释放分布式锁
- 可选择性清理旧索引

## 数据处理自定义

在迁移过程中，您可以自定义数据处理逻辑：

```java
// 在EsIndexMigrationService中修改此方法
private void processProductForMigration(EsProduct product) {
    // 1. 为新字段设置默认值
    if (product.getNewField() == null) {
        product.setNewField("default_value");
    }
    
    // 2. 数据格式转换
    if (product.getCreateTime() != null) {
        // 转换时间格式等
    }
    
    // 3. 数据清洗
    if (product.getProductName() != null) {
        product.setProductName(product.getProductName().trim());
    }
    
    // 4. 计算衍生字段
    if (product.getDropShippingPrice() != null && product.getPickUpPrice() != null) {
        BigDecimal avgPrice = product.getDropShippingPrice()
            .add(product.getPickUpPrice())
            .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
        product.setMainPrice(avgPrice);
    }
}
```

## 监控和告警

### 进度监控
```java
// 定期检查迁移进度
MigrationProgressDto progress = migrationService.getMigrationProgress(migrationId);
if (progress != null) {
    log.info("迁移状态: {}", progress.getStatus().getDescription());
    log.info("进度: {}%", progress.getProgressPercentage());
    log.info("已迁移: {}/{}", progress.getMigratedCount(), progress.getTotalCount());
}
```

### 状态说明
- `STARTED`: 迁移已启动
- `CHECKING`: 检查索引状态
- `CREATING_INDEX`: 创建新索引
- `MIGRATING_DATA`: 数据迁移中
- `VALIDATING`: 验证数据完整性
- `SWITCHING_ALIAS`: 切换索引别名
- `COMPLETED`: 迁移完成
- `FAILED`: 迁移失败

## 最佳实践

### 1. 迁移前准备
- 在测试环境完整验证迁移流程
- 备份重要数据
- 评估迁移时间和资源需求
- 制定回滚方案

### 2. 迁移执行
- 选择业务低峰期执行
- 监控ES集群资源使用情况
- 实时关注迁移进度和错误日志
- 准备应急处理方案

### 3. 迁移后验证
- 验证数据完整性和一致性
- 测试业务功能正常
- 监控新索引性能表现
- 保留旧索引一段时间后再清理

### 4. 性能优化
- 根据数据量调整批处理大小
- 合理设置线程池参数
- 监控内存使用情况
- 必要时调整ES集群配置

## 故障处理

### 常见问题
1. **迁移中断**: 检查网络连接和ES集群状态
2. **数据不一致**: 验证源数据和处理逻辑
3. **内存溢出**: 减小批处理大小
4. **超时失败**: 增加超时时间配置

### 回滚方案
1. 停止当前迁移任务
2. 将别名切回旧索引
3. 删除问题索引
4. 分析问题原因后重新迁移

## 注意事项

1. **数据安全**: 迁移前务必备份重要数据
2. **资源消耗**: 大数据量迁移会消耗较多资源
3. **业务影响**: 建议在业务低峰期执行
4. **版本兼容**: 确保Easy-Es版本支持所需功能
5. **权限控制**: 迁移操作需要管理员权限

## 技术支持

如遇到问题，请检查：
1. 日志文件中的详细错误信息
2. ES集群健康状态
3. Redis连接状态
4. 线程池配置是否合理

更多技术细节请参考源码注释和Easy-Es官方文档。
