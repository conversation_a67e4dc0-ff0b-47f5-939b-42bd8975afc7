package com.zsmall.extend.es.migration.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.zsmall.extend.es.migration.EsIndexMigrationService;
import com.zsmall.extend.es.migration.dto.MigrationProgressDto;
import com.zsmall.extend.es.migration.dto.MigrationResultDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * ES索引迁移控制器
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/es/migration")
@RequiredArgsConstructor
@Tag(name = "ES索引迁移管理", description = "ES索引迁移相关接口")
public class EsIndexMigrationController {

    private final EsIndexMigrationService migrationService;
    private final ThreadPoolExecutor migrationThreadPool;

    /**
     * 启动索引迁移
     */
    @PostMapping("/start")
    @Operation(summary = "启动索引迁移")
    @Log(title = "ES索引迁移", businessType = BusinessType.OTHER)
    @SaCheckRole("admin")
    public R<String> startMigration(
            @Parameter(description = "旧索引名称", required = true)
            @RequestParam @NotBlank(message = "旧索引名称不能为空") String oldIndexName,
            
            @Parameter(description = "新索引名称", required = true)
            @RequestParam @NotBlank(message = "新索引名称不能为空") String newIndexName,
            
            @Parameter(description = "别名名称")
            @RequestParam(required = false) String aliasName,
            
            @Parameter(description = "是否异步执行", required = false)
            @RequestParam(defaultValue = "true") Boolean async) {

        try {
            if (async) {
                // 异步执行迁移
                CompletableFuture<MigrationResultDto> future = CompletableFuture.supplyAsync(
                    () -> migrationService.performIndexMigration(oldIndexName, newIndexName, aliasName),
                    migrationThreadPool
                );
                
                return R.ok("迁移任务已启动，请通过进度查询接口查看迁移状态");
            } else {
                // 同步执行迁移
                MigrationResultDto result = migrationService.performIndexMigration(oldIndexName, newIndexName, aliasName);
                if (result.getSuccess()) {
                    return R.ok(result.getMigrationId(), result.getMessage());
                } else {
                    return R.fail(result.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("启动索引迁移失败", e);
            return R.fail("启动迁移失败: " + e.getMessage());
        }
    }

    /**
     * 查询迁移进度
     */
    @GetMapping("/progress/{migrationId}")
    @Operation(summary = "查询迁移进度")
    public R<MigrationProgressDto> getMigrationProgress(
            @Parameter(description = "迁移ID", required = true)
            @PathVariable @NotBlank(message = "迁移ID不能为空") String migrationId) {
        
        try {
            MigrationProgressDto progress = migrationService.getMigrationProgress(migrationId);
            if (progress == null) {
                return R.fail("未找到迁移任务: " + migrationId);
            }
            return R.ok(progress);
        } catch (Exception e) {
            log.error("查询迁移进度失败，迁移ID: {}", migrationId, e);
            return R.fail("查询进度失败: " + e.getMessage());
        }
    }

    /**
     * 查询迁移结果
     */
    @GetMapping("/result/{migrationId}")
    @Operation(summary = "查询迁移结果")
    public R<MigrationResultDto> getMigrationResult(
            @Parameter(description = "迁移ID", required = true)
            @PathVariable @NotBlank(message = "迁移ID不能为空") String migrationId) {
        
        try {
            MigrationResultDto result = migrationService.getMigrationResult(migrationId);
            if (result == null) {
                return R.fail("未找到迁移结果: " + migrationId);
            }
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询迁移结果失败，迁移ID: {}", migrationId, e);
            return R.fail("查询结果失败: " + e.getMessage());
        }
    }

    /**
     * 清理旧索引
     */
    @DeleteMapping("/cleanup/{oldIndexName}")
    @Operation(summary = "清理旧索引")
    @Log(title = "清理ES索引", businessType = BusinessType.DELETE)
    @SaCheckRole("admin")
    public R<Void> cleanupOldIndex(
            @Parameter(description = "旧索引名称", required = true)
            @PathVariable @NotBlank(message = "索引名称不能为空") String oldIndexName) {
        
        try {
            boolean result = migrationService.cleanupOldIndex(oldIndexName);
            if (result) {
                return R.ok("索引清理成功");
            } else {
                return R.fail("索引清理失败");
            }
        } catch (Exception e) {
            log.error("清理索引失败，索引名称: {}", oldIndexName, e);
            return R.fail("清理索引失败: " + e.getMessage());
        }
    }

    /**
     * 检查索引状态
     */
    @GetMapping("/check/{indexName}")
    @Operation(summary = "检查索引状态")
    public R<Boolean> checkIndexExists(
            @Parameter(description = "索引名称", required = true)
            @PathVariable @NotBlank(message = "索引名称不能为空") String indexName) {
        
        try {
            // 这里需要在EsIndexMigrationService中添加相应方法
            // boolean exists = migrationService.checkIndexExists(indexName);
            // return R.ok(exists, exists ? "索引存在" : "索引不存在");
            return R.ok(true, "功能待实现");
        } catch (Exception e) {
            log.error("检查索引状态失败，索引名称: {}", indexName, e);
            return R.fail("检查索引状态失败: " + e.getMessage());
        }
    }
}
