package com.zsmall.extend.es.migration.dto;

import com.zsmall.extend.es.migration.enums.MigrationStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 迁移进度DTO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
public class MigrationProgressDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 迁移ID
     */
    private String migrationId;

    /**
     * 旧索引名称
     */
    private String oldIndexName;

    /**
     * 新索引名称
     */
    private String newIndexName;

    /**
     * 迁移状态
     */
    private MigrationStatusEnum status;

    /**
     * 进度消息
     */
    private String message;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 已迁移数量
     */
    private Long migratedCount;

    /**
     * 总数量
     */
    private Long totalCount;

    /**
     * 进度百分比
     */
    private Double progressPercentage;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 计算进度百分比
     */
    public void calculateProgress() {
        if (totalCount != null && totalCount > 0 && migratedCount != null) {
            this.progressPercentage = (double) migratedCount / totalCount * 100;
        }
    }

    /**
     * 获取耗时（毫秒）
     */
    public Long getDurationMillis() {
        if (startTime != null && endTime != null) {
            return endTime.getTime() - startTime.getTime();
        }
        if (startTime != null && updateTime != null) {
            return updateTime.getTime() - startTime.getTime();
        }
        return null;
    }

    /**
     * 获取格式化的耗时
     */
    public String getFormattedDuration() {
        Long duration = getDurationMillis();
        if (duration == null) {
            return "未知";
        }
        
        long seconds = duration / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
