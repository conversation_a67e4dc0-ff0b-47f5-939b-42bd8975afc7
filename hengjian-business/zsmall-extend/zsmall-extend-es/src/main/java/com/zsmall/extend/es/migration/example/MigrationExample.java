package com.zsmall.extend.es.migration.example;

import com.zsmall.extend.es.migration.EsIndexMigrationService;
import com.zsmall.extend.es.migration.dto.MigrationProgressDto;
import com.zsmall.extend.es.migration.dto.MigrationResultDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ES索引迁移使用示例
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MigrationExample {

    private final EsIndexMigrationService migrationService;

    /**
     * 示例1：基本迁移流程
     * 
     * 场景：修改了EsProduct实体类的字段，需要重建索引
     */
    public void basicMigrationExample() {
        log.info("=== 基本迁移流程示例 ===");
        
        String oldIndexName = "product";
        String newIndexName = "product_v2";
        String aliasName = "product_current";
        
        try {
            // 1. 启动迁移
            log.info("启动索引迁移...");
            MigrationResultDto result = migrationService.performIndexMigration(
                oldIndexName, newIndexName, aliasName);
            
            if (result.getSuccess()) {
                log.info("迁移成功完成！");
                log.info("迁移ID: {}", result.getMigrationId());
                log.info("迁移数据量: {}", result.getMigratedCount());
                log.info("耗时: {}", result.getFormattedDuration());
            } else {
                log.error("迁移失败: {}", result.getMessage());
                if (result.getErrorDetail() != null) {
                    log.error("错误详情: {}", result.getErrorDetail());
                }
            }
            
        } catch (Exception e) {
            log.error("迁移过程中发生异常", e);
        }
    }

    /**
     * 示例2：异步迁移流程
     * 
     * 场景：大数据量迁移，使用异步方式避免阻塞
     */
    public void asyncMigrationExample() {
        log.info("=== 异步迁移流程示例 ===");
        
        String oldIndexName = "product";
        String newIndexName = "product_v3";
        
        // 异步执行迁移
        new Thread(() -> {
            try {
                MigrationResultDto result = migrationService.performIndexMigration(
                    oldIndexName, newIndexName, null);
                
                String migrationId = result.getMigrationId();
                
                // 定期检查进度
                while (true) {
                    MigrationProgressDto progress = migrationService.getMigrationProgress(migrationId);
                    if (progress == null) {
                        log.warn("未找到迁移进度信息");
                        break;
                    }
                    
                    log.info("迁移进度: {} - {}", progress.getStatus().getDescription(), progress.getMessage());
                    
                    if (progress.getStatus().isTerminal()) {
                        if (progress.getStatus().isSuccess()) {
                            log.info("异步迁移成功完成！");
                        } else {
                            log.error("异步迁移失败: {}", progress.getErrorMessage());
                        }
                        break;
                    }
                    
                    // 等待5秒后再次检查
                    Thread.sleep(5000);
                }
                
            } catch (Exception e) {
                log.error("异步迁移过程中发生异常", e);
            }
        }).start();
    }

    /**
     * 示例3：字段变更迁移
     * 
     * 场景：修改了字段类型或添加了新字段
     */
    public void fieldChangeMigrationExample() {
        log.info("=== 字段变更迁移示例 ===");
        
        /*
         * 假设我们修改了EsProduct实体类：
         * 
         * 1. 修改了productName字段的分词器：
         *    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_MAX_WORD)
         *    private String productName;
         * 
         * 2. 添加了新字段：
         *    @IndexField(fieldType = FieldType.KEYWORD)
         *    private String newField;
         * 
         * 3. 修改了价格字段的精度：
         *    @IndexField(fieldType = FieldType.SCALED_FLOAT, scalingFactor = 100)
         *    private BigDecimal dropShippingPrice;
         */
        
        String oldIndexName = "product";
        String newIndexName = "product_field_updated";
        String aliasName = "product_current";
        
        try {
            log.info("开始字段变更迁移...");
            
            // 执行迁移
            MigrationResultDto result = migrationService.performIndexMigration(
                oldIndexName, newIndexName, aliasName);
            
            if (result.getSuccess()) {
                log.info("字段变更迁移成功！");
                
                // 可选：清理旧索引（建议保留一段时间后再清理）
                log.info("建议保留旧索引一段时间后再清理，确保新索引稳定运行");
                // migrationService.cleanupOldIndex(oldIndexName);
                
            } else {
                log.error("字段变更迁移失败: {}", result.getMessage());
            }
            
        } catch (Exception e) {
            log.error("字段变更迁移过程中发生异常", e);
        }
    }

    /**
     * 示例4：批量数据处理迁移
     * 
     * 场景：需要在迁移过程中对数据进行处理
     */
    public void dataProcessingMigrationExample() {
        log.info("=== 数据处理迁移示例 ===");
        
        /*
         * 在EsIndexMigrationService的processProductForMigration方法中
         * 可以添加数据处理逻辑：
         * 
         * private void processProductForMigration(EsProduct product) {
         *     // 1. 为新字段设置默认值
         *     if (product.getNewField() == null) {
         *         product.setNewField("default_value");
         *     }
         *     
         *     // 2. 数据格式转换
         *     if (product.getCreateTime() != null) {
         *         // 转换时间格式
         *         product.setCreateTime(convertTimeFormat(product.getCreateTime()));
         *     }
         *     
         *     // 3. 数据清洗
         *     if (product.getProductName() != null) {
         *         product.setProductName(product.getProductName().trim());
         *     }
         *     
         *     // 4. 计算衍生字段
         *     if (product.getDropShippingPrice() != null && product.getPickUpPrice() != null) {
         *         BigDecimal avgPrice = product.getDropShippingPrice()
         *             .add(product.getPickUpPrice())
         *             .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
         *         product.setMainPrice(avgPrice);
         *     }
         * }
         */
        
        String oldIndexName = "product";
        String newIndexName = "product_processed";
        
        try {
            log.info("开始数据处理迁移...");
            
            MigrationResultDto result = migrationService.performIndexMigration(
                oldIndexName, newIndexName, null);
            
            if (result.getSuccess()) {
                log.info("数据处理迁移成功！处理了 {} 条数据", result.getMigratedCount());
            } else {
                log.error("数据处理迁移失败: {}", result.getMessage());
            }
            
        } catch (Exception e) {
            log.error("数据处理迁移过程中发生异常", e);
        }
    }

    /**
     * 示例5：迁移状态监控
     * 
     * 场景：监控迁移过程，及时发现问题
     */
    public void migrationMonitoringExample() {
        log.info("=== 迁移状态监控示例 ===");
        
        String migrationId = "migration_product_to_product_v4_20240115_143000";
        
        try {
            // 查询迁移进度
            MigrationProgressDto progress = migrationService.getMigrationProgress(migrationId);
            if (progress != null) {
                log.info("迁移状态: {}", progress.getStatus().getDescription());
                log.info("进度消息: {}", progress.getMessage());
                log.info("已迁移数量: {}", progress.getMigratedCount());
                log.info("总数量: {}", progress.getTotalCount());
                log.info("进度百分比: {}%", progress.getProgressPercentage());
                log.info("耗时: {}", progress.getFormattedDuration());
            }
            
            // 查询迁移结果
            MigrationResultDto result = migrationService.getMigrationResult(migrationId);
            if (result != null) {
                log.info("迁移结果: {}", result.getSuccess() ? "成功" : "失败");
                log.info("结果消息: {}", result.getMessage());
                log.info("迁移数据量: {}", result.getMigratedCount());
                log.info("总耗时: {}", result.getFormattedDuration());
            }
            
        } catch (Exception e) {
            log.error("查询迁移状态失败", e);
        }
    }
}
