package com.zsmall.extend.es.migration.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 迁移结果DTO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
public class MigrationResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 迁移ID
     */
    private String migrationId;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 结果消息
     */
    private String message;

    /**
     * 迁移数据量
     */
    private Long migratedCount;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 耗时（毫秒）
     */
    private Long durationMillis;

    /**
     * 错误详情
     */
    private String errorDetail;

    /**
     * 旧索引名称
     */
    private String oldIndexName;

    /**
     * 新索引名称
     */
    private String newIndexName;

    /**
     * 别名名称
     */
    private String aliasName;

    /**
     * 创建成功结果
     */
    public static MigrationResultDto success(String message) {
        MigrationResultDto result = new MigrationResultDto();
        result.setSuccess(true);
        result.setMessage(message);
        result.setEndTime(new Date());
        return result;
    }

    /**
     * 创建失败结果
     */
    public static MigrationResultDto fail(String message) {
        MigrationResultDto result = new MigrationResultDto();
        result.setSuccess(false);
        result.setMessage(message);
        result.setEndTime(new Date());
        return result;
    }

    /**
     * 创建失败结果（带错误详情）
     */
    public static MigrationResultDto fail(String message, String errorDetail) {
        MigrationResultDto result = fail(message);
        result.setErrorDetail(errorDetail);
        return result;
    }

    /**
     * 计算耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.durationMillis = endTime.getTime() - startTime.getTime();
        }
    }

    /**
     * 获取格式化的耗时
     */
    public String getFormattedDuration() {
        if (durationMillis == null) {
            return "未知";
        }
        
        long seconds = durationMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
