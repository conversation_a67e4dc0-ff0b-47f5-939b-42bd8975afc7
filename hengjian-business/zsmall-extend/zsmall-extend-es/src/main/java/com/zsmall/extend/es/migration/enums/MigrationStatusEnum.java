package com.zsmall.extend.es.migration.enums;

/**
 * 迁移状态枚举
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public enum MigrationStatusEnum {

    /**
     * 已启动
     */
    STARTED("STARTED", "已启动"),

    /**
     * 检查中
     */
    CHECKING("CHECKING", "检查索引状态"),

    /**
     * 创建索引中
     */
    CREATING_INDEX("CREATING_INDEX", "创建新索引"),

    /**
     * 数据迁移中
     */
    MIGRATING_DATA("MIGRATING_DATA", "数据迁移中"),

    /**
     * 验证中
     */
    VALIDATING("VALIDATING", "验证数据完整性"),

    /**
     * 切换别名中
     */
    SWITCHING_ALIAS("SWITCHING_ALIAS", "切换索引别名"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "迁移完成"),

    /**
     * 失败
     */
    FAILED("FAILED", "迁移失败"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "迁移已取消");

    private final String code;
    private final String description;

    MigrationStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static MigrationStatusEnum fromCode(String code) {
        for (MigrationStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的迁移状态代码: " + code);
    }

    /**
     * 是否为终态
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == CANCELLED;
    }

    /**
     * 是否为成功状态
     */
    public boolean isSuccess() {
        return this == COMPLETED;
    }

    /**
     * 是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED || this == CANCELLED;
    }
}
