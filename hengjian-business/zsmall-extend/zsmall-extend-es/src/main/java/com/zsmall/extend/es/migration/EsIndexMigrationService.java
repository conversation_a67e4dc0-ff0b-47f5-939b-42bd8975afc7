package com.zsmall.extend.es.migration;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.zsmall.extend.es.entity.EsProduct;
import com.zsmall.extend.es.esmapper.EsProductMapper;
import com.zsmall.extend.es.migration.dto.MigrationProgressDto;
import com.zsmall.extend.es.migration.dto.MigrationResultDto;
import com.zsmall.extend.es.migration.enums.MigrationStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.conditions.update.LambdaEsUpdateWrapper;
import org.dromara.easyes.core.toolkit.EsWrappers;
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest;
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.GetAliasesResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.GetIndexResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * ES索引迁移服务
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EsIndexMigrationService {

    private final EsProductMapper esProductMapper;
    private final RestHighLevelClient restHighLevelClient;
    private final ThreadPoolExecutor migrationThreadPool;

    // Redis缓存键前缀
    private static final String MIGRATION_LOCK_KEY = "es:migration:lock:";
    private static final String MIGRATION_PROGRESS_KEY = "es:migration:progress:";
    private static final String MIGRATION_RESULT_KEY = "es:migration:result:";

    // 迁移配置
    private static final int BATCH_SIZE = 1000;
    private static final int MAX_RETRY_TIMES = 3;
    private static final long LOCK_EXPIRE_TIME = 3600; // 1小时

    /**
     * 执行完整的索引迁移流程
     *
     * @param oldIndexName 旧索引名称
     * @param newIndexName 新索引名称
     * @param aliasName 别名（可选）
     * @return 迁移结果
     */
    public MigrationResultDto performIndexMigration(String oldIndexName, String newIndexName, String aliasName) {
        String migrationId = generateMigrationId(oldIndexName, newIndexName);
        String lockKey = MIGRATION_LOCK_KEY + migrationId;

        // 获取分布式锁
        if (!RedisUtils.setIfAbsent(lockKey, "locked", LOCK_EXPIRE_TIME, TimeUnit.SECONDS)) {
            return MigrationResultDto.fail("迁移任务正在进行中，请稍后再试");
        }

        try {
            log.info("开始执行索引迁移，迁移ID: {}, 旧索引: {}, 新索引: {}", migrationId, oldIndexName, newIndexName);

            // 初始化进度
            initMigrationProgress(migrationId, oldIndexName, newIndexName);

            // 执行迁移流程
            return executeCompleteMigration(migrationId, oldIndexName, newIndexName, aliasName);

        } catch (Exception e) {
            log.error("索引迁移失败，迁移ID: {}", migrationId, e);
            updateMigrationProgress(migrationId, MigrationStatusEnum.FAILED, "迁移过程中发生异常: " + e.getMessage());
            return MigrationResultDto.fail("迁移失败: " + e.getMessage());
        } finally {
            // 释放锁
            RedisUtils.deleteObject(lockKey);
        }
    }

    /**
     * 执行完整迁移流程
     */
    private MigrationResultDto executeCompleteMigration(String migrationId, String oldIndexName,
                                                       String newIndexName, String aliasName) {
        try {
            // 步骤1: 检查索引状态
            updateMigrationProgress(migrationId, MigrationStatusEnum.CHECKING, "检查索引状态");
            if (!checkIndexStatus(oldIndexName, newIndexName)) {
                return MigrationResultDto.fail("索引状态检查失败");
            }

            // 步骤2: 创建新索引
            updateMigrationProgress(migrationId, MigrationStatusEnum.CREATING_INDEX, "创建新索引");
            if (!createNewIndex(newIndexName)) {
                return MigrationResultDto.fail("创建新索引失败");
            }

            // 步骤3: 数据迁移
            updateMigrationProgress(migrationId, MigrationStatusEnum.MIGRATING_DATA, "开始数据迁移");
            long migratedCount = migrateData(migrationId, oldIndexName, newIndexName);
            if (migratedCount < 0) {
                return MigrationResultDto.fail("数据迁移失败");
            }

            // 步骤4: 数据验证
            updateMigrationProgress(migrationId, MigrationStatusEnum.VALIDATING, "验证数据完整性");
            if (!validateDataIntegrity(oldIndexName, newIndexName, migratedCount)) {
                return MigrationResultDto.fail("数据验证失败");
            }

            // 步骤5: 切换别名（如果提供了别名）
            if (StrUtil.isNotBlank(aliasName)) {
                updateMigrationProgress(migrationId, MigrationStatusEnum.SWITCHING_ALIAS, "切换索引别名");
                if (!switchAlias(oldIndexName, newIndexName, aliasName)) {
                    return MigrationResultDto.fail("切换别名失败");
                }
            }

            // 步骤6: 完成迁移
            updateMigrationProgress(migrationId, MigrationStatusEnum.COMPLETED, "迁移完成");

            MigrationResultDto result = MigrationResultDto.success("迁移成功完成");
            result.setMigratedCount(migratedCount);
            result.setMigrationId(migrationId);

            // 保存迁移结果
            saveMigrationResult(migrationId, result);

            log.info("索引迁移成功完成，迁移ID: {}, 迁移数据量: {}", migrationId, migratedCount);
            return result;

        } catch (Exception e) {
            log.error("执行迁移流程失败，迁移ID: {}", migrationId, e);
            updateMigrationProgress(migrationId, MigrationStatusEnum.FAILED, "迁移失败: " + e.getMessage());
            return MigrationResultDto.fail("迁移失败: " + e.getMessage());
        }
    }

    /**
     * 检查索引状态
     */
    private boolean checkIndexStatus(String oldIndexName, String newIndexName) {
        try {
            // 检查旧索引是否存在
            if (!esProductMapper.existsIndex(oldIndexName)) {
                log.error("旧索引不存在: {}", oldIndexName);
                return false;
            }

            // 检查新索引是否已存在
            if (esProductMapper.existsIndex(newIndexName)) {
                log.warn("新索引已存在，将先删除: {}", newIndexName);
                esProductMapper.deleteIndex(newIndexName);
            }

            return true;
        } catch (Exception e) {
            log.error("检查索引状态失败", e);
            return false;
        }
    }

    /**
     * 创建新索引
     */
    private boolean createNewIndex(String newIndexName) {
        try {
            // 使用实体类注解创建索引
            boolean result = esProductMapper.createIndex();
            if (result) {
                log.info("新索引创建成功: {}", newIndexName);
                return true;
            } else {
                log.error("新索引创建失败: {}", newIndexName);
                return false;
            }
        } catch (Exception e) {
            log.error("创建新索引失败: {}", newIndexName, e);
            return false;
        }
    }

    /**
     * 数据迁移
     */
    private long migrateData(String migrationId, String oldIndexName, String newIndexName) {
        try {
            // 获取总数据量
            LambdaEsQueryWrapper<EsProduct> countWrapper = new LambdaEsQueryWrapper<>();
            countWrapper.index(oldIndexName);
            long totalCount = esProductMapper.selectCount(countWrapper);

            log.info("开始数据迁移，总数据量: {}", totalCount);

            long migratedCount = 0;
            int pageNum = 1;
            int retryCount = 0;

            while (migratedCount < totalCount && retryCount < MAX_RETRY_TIMES) {
                try {
                    // 分页查询数据
                    LambdaEsQueryWrapper<EsProduct> queryWrapper = new LambdaEsQueryWrapper<>();
                    queryWrapper.index(oldIndexName);

                    List<EsProduct> products = esProductMapper.selectList(queryWrapper, pageNum, BATCH_SIZE);

                    if (CollUtil.isEmpty(products)) {
                        break;
                    }

                    // 批量插入到新索引
                    // 临时设置索引名称到新索引
                    products.forEach(product -> {
                        // 这里可以对数据进行处理，比如添加新字段等
                        processProductForMigration(product);
                    });

                    // 插入到新索引
                    int insertCount = esProductMapper.insertBatch(products);
                    migratedCount += insertCount;

                    // 更新进度
                    double progress = (double) migratedCount / totalCount * 100;
                    updateMigrationProgress(migrationId, MigrationStatusEnum.MIGRATING_DATA,
                        String.format("数据迁移中: %d/%d (%.2f%%)", migratedCount, totalCount, progress));

                    log.info("批次迁移完成，页码: {}, 本批数量: {}, 累计迁移: {}/{}",
                        pageNum, insertCount, migratedCount, totalCount);

                    pageNum++;
                    retryCount = 0; // 重置重试次数

                    // 避免过快操作ES
                    Thread.sleep(100);

                } catch (Exception e) {
                    retryCount++;
                    log.warn("数据迁移批次失败，页码: {}, 重试次数: {}/{}", pageNum, retryCount, MAX_RETRY_TIMES, e);

                    if (retryCount >= MAX_RETRY_TIMES) {
                        log.error("数据迁移失败，超过最大重试次数");
                        return -1;
                    }

                    // 重试前等待
                    Thread.sleep(1000 * retryCount);
                }
            }

            log.info("数据迁移完成，总迁移数量: {}", migratedCount);
            return migratedCount;

        } catch (Exception e) {
            log.error("数据迁移过程失败", e);
            return -1;
        }
    }

    /**
     * 处理迁移中的商品数据
     * 可以在这里添加新字段或修改现有字段
     */
    private void processProductForMigration(EsProduct product) {
        // 示例：为新字段设置默认值
        // if (product.getNewField() == null) {
        //     product.setNewField("default_value");
        // }

        // 示例：数据格式转换
        // if (product.getCreateTime() != null) {
        //     // 转换时间格式等
        // }
    }

    /**
     * 验证数据完整性
     */
    private boolean validateDataIntegrity(String oldIndexName, String newIndexName, long expectedCount) {
        try {
            // 验证数据量
            LambdaEsQueryWrapper<EsProduct> newWrapper = new LambdaEsQueryWrapper<>();
            newWrapper.index(newIndexName);
            long newCount = esProductMapper.selectCount(newWrapper);

            if (newCount != expectedCount) {
                log.error("数据验证失败，期望数量: {}, 实际数量: {}", expectedCount, newCount);
                return false;
            }

            // 抽样验证数据一致性
            return validateSampleData(oldIndexName, newIndexName);

        } catch (Exception e) {
            log.error("数据验证过程失败", e);
            return false;
        }
    }

    /**
     * 抽样验证数据一致性
     */
    private boolean validateSampleData(String oldIndexName, String newIndexName) {
        try {
            // 随机抽取100条数据进行验证
            LambdaEsQueryWrapper<EsProduct> oldWrapper = new LambdaEsQueryWrapper<>();
            oldWrapper.index(oldIndexName);
            List<EsProduct> oldSamples = esProductMapper.selectList(oldWrapper, 1, 100);

            for (EsProduct oldProduct : oldSamples) {
                LambdaEsQueryWrapper<EsProduct> newWrapper = new LambdaEsQueryWrapper<>();
                newWrapper.index(newIndexName).eq(EsProduct::getId, oldProduct.getId());
                EsProduct newProduct = esProductMapper.selectOne(newWrapper);

                if (newProduct == null) {
                    log.error("数据验证失败，新索引中缺少数据，ID: {}", oldProduct.getId());
                    return false;
                }

                // 验证关键字段
                if (!validateProductFields(oldProduct, newProduct)) {
                    log.error("数据验证失败，字段不一致，ID: {}", oldProduct.getId());
                    return false;
                }
            }

            log.info("抽样数据验证通过");
            return true;

        } catch (Exception e) {
            log.error("抽样数据验证失败", e);
            return false;
        }
    }

    /**
     * 验证商品字段一致性
     */
    private boolean validateProductFields(EsProduct oldProduct, EsProduct newProduct) {
        // 验证关键字段
        return Objects.equals(oldProduct.getSkuCode(), newProduct.getSkuCode()) &&
               Objects.equals(oldProduct.getSpuCode(), newProduct.getSpuCode()) &&
               Objects.equals(oldProduct.getProductName(), newProduct.getProductName()) &&
               Objects.equals(oldProduct.getDropShippingPrice(), newProduct.getDropShippingPrice());
    }

    /**
     * 切换索引别名
     */
    private boolean switchAlias(String oldIndexName, String newIndexName, String aliasName) {
        try {
            IndicesAliasesRequest request = new IndicesAliasesRequest();

            // 检查别名是否存在
            GetAliasesRequest getAliasRequest = new GetAliasesRequest(aliasName);
            GetAliasesResponse aliasResponse = restHighLevelClient.indices().getAlias(getAliasRequest, RequestOptions.DEFAULT);

            // 如果别名存在，先从旧索引移除
            if (!aliasResponse.getAliases().isEmpty()) {
                request.addAliasAction(IndicesAliasesRequest.AliasActions.remove()
                    .index(oldIndexName)
                    .alias(aliasName));
            }

            // 将别名添加到新索引
            request.addAliasAction(IndicesAliasesRequest.AliasActions.add()
                .index(newIndexName)
                .alias(aliasName));

            AcknowledgedResponse response = restHighLevelClient.indices().updateAliases(request, RequestOptions.DEFAULT);

            if (response.isAcknowledged()) {
                log.info("别名切换成功，别名: {}, 从 {} 切换到 {}", aliasName, oldIndexName, newIndexName);
                return true;
            } else {
                log.error("别名切换失败，别名: {}", aliasName);
                return false;
            }

        } catch (IOException e) {
            log.error("切换别名过程失败，别名: {}", aliasName, e);
            return false;
        }
    }

    /**
     * 清理旧索引
     */
    public boolean cleanupOldIndex(String oldIndexName) {
        try {
            boolean result = esProductMapper.deleteIndex(oldIndexName);
            if (result) {
                log.info("旧索引清理成功: {}", oldIndexName);
            } else {
                log.error("旧索引清理失败: {}", oldIndexName);
            }
            return result;
        } catch (Exception e) {
            log.error("清理旧索引失败: {}", oldIndexName, e);
            return false;
        }
    }

    /**
     * 生成迁移ID
     */
    private String generateMigrationId(String oldIndexName, String newIndexName) {
        return String.format("migration_%s_to_%s_%s",
            oldIndexName, newIndexName, DateUtil.format(new Date(), "yyyyMMdd_HHmmss"));
    }

    /**
     * 初始化迁移进度
     */
    private void initMigrationProgress(String migrationId, String oldIndexName, String newIndexName) {
        MigrationProgressDto progress = new MigrationProgressDto();
        progress.setMigrationId(migrationId);
        progress.setOldIndexName(oldIndexName);
        progress.setNewIndexName(newIndexName);
        progress.setStatus(MigrationStatusEnum.STARTED);
        progress.setMessage("迁移任务已启动");
        progress.setStartTime(new Date());

        RedisUtils.setCacheObject(MIGRATION_PROGRESS_KEY + migrationId, progress, 24, TimeUnit.HOURS);
    }

    /**
     * 更新迁移进度
     */
    private void updateMigrationProgress(String migrationId, MigrationStatusEnum status, String message) {
        try {
            MigrationProgressDto progress = RedisUtils.getCacheObject(MIGRATION_PROGRESS_KEY + migrationId);
            if (progress != null) {
                progress.setStatus(status);
                progress.setMessage(message);
                progress.setUpdateTime(new Date());

                if (status == MigrationStatusEnum.COMPLETED || status == MigrationStatusEnum.FAILED) {
                    progress.setEndTime(new Date());
                }

                RedisUtils.setCacheObject(MIGRATION_PROGRESS_KEY + migrationId, progress, 24, TimeUnit.HOURS);
                log.info("迁移进度更新 - ID: {}, 状态: {}, 消息: {}", migrationId, status, message);
            }
        } catch (Exception e) {
            log.error("更新迁移进度失败，迁移ID: {}", migrationId, e);
        }
    }

    /**
     * 保存迁移结果
     */
    private void saveMigrationResult(String migrationId, MigrationResultDto result) {
        try {
            RedisUtils.setCacheObject(MIGRATION_RESULT_KEY + migrationId, result, 7, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("保存迁移结果失败，迁移ID: {}", migrationId, e);
        }
    }

    /**
     * 获取迁移进度
     */
    public MigrationProgressDto getMigrationProgress(String migrationId) {
        return RedisUtils.getCacheObject(MIGRATION_PROGRESS_KEY + migrationId);
    }

    /**
     * 获取迁移结果
     */
    public MigrationResultDto getMigrationResult(String migrationId) {
        return RedisUtils.getCacheObject(MIGRATION_RESULT_KEY + migrationId);
    }
}
