1. 创建账单明细数据修补表 <font style="color:#bcbec4;background-color:#1e1f22;">bill_details_repair</font>
2. Controller 接口接收参数 <font style="color:#bcbec4;background-color:#1e1f22;">String startTime, String endTime, String billNos, String orderNo，String tenantId, 多个订单号用,拼接</font>
3. 将原账单数据<font style="color:#bcbec4;background-color:#1e1f22;">bill_details</font>中要修补的订单号所属账单明细数据记录到修补表中
4. 删除这个租户 当前当前月的账单数据 <font style="color:#bcbec4;background-color:#1e1f22;">bill_head/bill_details</font>
5. 删除这个租户当前月的账单汇总数据<font style="color:#bcbec4;background-color:#1e1f22;">bill_abstract_detail</font>
6. 调用这个方法重新生成当月的账单数据<font style="color:#bcbec4;background-color:#1e1f22;">billHeadSupper.generatedDistributorBillByTenant</font>
7. 调用这个方法生成当月的账单汇总数据<font style="color:#bcbec4;background-color:#1e1f22;">billHeadSupper.generatedTransactionReceipt</font>
8. 然后用修补表中的订单所属账单数据中金额字段和bill_details 表中订单所属账单数据中金额字段进行差值计算，例如

